'use client';

import React, { useEffect, useState } from 'react';

interface PageLoadingIndicatorProps {
  isLoading?: boolean;
  duration?: number;
  className?: string;
}

/**
 * Professional page loading indicator like Facebook
 * - Lightweight and non-blocking
 * - Shows briefly during initial loads
 * - Maintains super fast performance
 */
export default function PageLoadingIndicator({ 
  isLoading = false, 
  duration = 800,
  className = ''
}: PageLoadingIndicatorProps) {
  const [show, setShow] = useState(isLoading);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (isLoading) {
      setShow(true);
      setProgress(0);

      // Smooth progress animation
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // Stop at 90% until loading completes
          }
          return prev + Math.random() * 15; // Random increments for realistic feel
        });
      }, 50);

      return () => clearInterval(progressInterval);
    } else {
      // Complete the progress bar quickly when loading finishes
      setProgress(100);
      
      // Hide after a brief moment
      const hideTimer = setTimeout(() => {
        setShow(false);
        setProgress(0);
      }, 200);

      return () => clearTimeout(hideTimer);
    }
  }, [isLoading]);

  if (!show) return null;

  return (
    <div className={`fixed top-0 left-0 right-0 z-[9999] ${className}`}>
      {/* Progress Bar */}
      <div className="h-1 bg-gray-200">
        <div 
          className="h-full bg-gradient-to-r from-orange-500 to-orange-600 transition-all duration-300 ease-out"
          style={{ 
            width: `${progress}%`,
            boxShadow: '0 0 10px rgba(243, 168, 35, 0.5)'
          }}
        />
      </div>

      {/* Optional Logo Overlay for Initial Loads */}
      {progress < 30 && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
          <div className="flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg border border-gray-100">
            <div className="w-6 h-6 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">T</span>
            </div>
            <span className="text-gray-700 font-medium text-sm">Tap2Go</span>
            <div className="w-4 h-4">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-500 border-t-transparent"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Lightweight top progress bar only (like YouTube/GitHub)
 */
export function TopProgressBar({ 
  isLoading = false, 
  className = '' 
}: { isLoading?: boolean; className?: string }) {
  const [progress, setProgress] = useState(0);
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isLoading) {
      setShow(true);
      setProgress(0);

      // Fast progress animation
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 85) {
            clearInterval(progressInterval);
            return 85;
          }
          return prev + Math.random() * 20;
        });
      }, 100);

      return () => clearInterval(progressInterval);
    } else {
      setProgress(100);
      const hideTimer = setTimeout(() => {
        setShow(false);
        setProgress(0);
      }, 300);

      return () => clearTimeout(hideTimer);
    }
  }, [isLoading]);

  if (!show) return null;

  return (
    <div className={`fixed top-0 left-0 right-0 z-[9999] h-1 ${className}`}>
      <div 
        className="h-full bg-gradient-to-r from-orange-500 via-orange-400 to-orange-600 transition-all duration-200 ease-out"
        style={{ 
          width: `${progress}%`,
          boxShadow: '0 0 8px rgba(243, 168, 35, 0.6)'
        }}
      />
    </div>
  );
}

/**
 * Minimal loading dot (like Twitter)
 */
export function LoadingDot({ 
  isLoading = false,
  size = 'sm'
}: { isLoading?: boolean; size?: 'sm' | 'md' | 'lg' }) {
  if (!isLoading) return null;

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  };

  return (
    <div className="fixed top-4 right-4 z-[9999]">
      <div className="bg-white/95 backdrop-blur-sm p-2 rounded-full shadow-lg border border-gray-100">
        <div className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-orange-500 border-t-transparent`} />
      </div>
    </div>
  );
}
