'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

/**
 * SSR-safe authentication hook that prevents hydration mismatches
 * by ensuring consistent rendering between server and client
 */
export function useSSRSafeAuth() {
  const auth = useAuth() as any;
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after first render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Return safe values during SSR
  if (!isHydrated) {
    return {
      user: null,
      loading: true,
      isAuthenticated: false,
      authError: null,
      isInitialized: false,
      isHydrated: false,
      signIn: auth.signIn,
      signUp: auth.signUp,
      signOut: auth.signOut,
      updateProfile: auth.updateProfile,
    };
  }

  // Return actual auth state after hydration
  return {
    user: auth.user,
    loading: auth.loading,
    isAuthenticated: !!auth.user,
    authError: auth.authError,
    isInitialized: auth.isInitialized,
    isHydrated: true,
    signIn: auth.signIn,
    signUp: auth.signUp,
    signOut: auth.signOut,
    updateProfile: auth.updateProfile,
  };
}

/**
 * Hook for components that need to show different content based on auth state
 * but want to avoid hydration mismatches
 */
export function useSSRSafeAuthState() {
  const { user, loading, isHydrated } = useSSRSafeAuth();

  return {
    user: isHydrated ? user : null,
    loading: isHydrated ? loading : true,
    isAuthenticated: isHydrated ? !!user : false,
    isHydrated,
    // Safe to use for conditional rendering
    canShowAuthContent: isHydrated && !loading,
    canShowUserContent: isHydrated && !loading && !!user,
    canShowGuestContent: isHydrated && !loading && !user,
  };
}
