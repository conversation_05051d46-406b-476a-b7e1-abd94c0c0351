'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User as FirebaseUser,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  updateProfile as updateFirebaseProfile,
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { User, AuthContextType } from '@/types';
import { createUser, getUser, updateUser, updateUserLastLogin } from '@/lib/database/users';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth state persistence keys
const AUTH_STORAGE_KEY = 'tap2go_auth_state';
const AUTH_USER_KEY = 'tap2go_user_data';

// Helper functions for auth state persistence
const getStoredAuthState = (): { hasUser: boolean; userData: User | null } => {
  if (typeof window === 'undefined') return { hasUser: false, userData: null };

  try {
    const hasUser = localStorage.getItem(AUTH_STORAGE_KEY) === 'true';
    const userData = localStorage.getItem(AUTH_USER_KEY);
    return {
      hasUser,
      userData: userData ? JSON.parse(userData) : null
    };
  } catch (error) {
    console.error('Error reading stored auth state:', error);
    return { hasUser: false, userData: null };
  }
};

const setStoredAuthState = (user: User | null) => {
  if (typeof window === 'undefined') return;

  try {
    if (user) {
      localStorage.setItem(AUTH_STORAGE_KEY, 'true');
      localStorage.setItem(AUTH_USER_KEY, JSON.stringify(user));
    } else {
      localStorage.removeItem(AUTH_STORAGE_KEY);
      localStorage.removeItem(AUTH_USER_KEY);
    }
  } catch (error) {
    console.error('Error storing auth state:', error);
  }
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  // Initialize with stored auth state to prevent layout shifts
  const [user, setUser] = useState<User | null>(() => {
    const stored = getStoredAuthState();
    return stored.userData;
  });

  const [loading, setLoading] = useState(true);
  const [initialAuthCheck, setInitialAuthCheck] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        if (firebaseUser) {
          // Get user data from Firestore using new database functions
          const userData = await getUser(firebaseUser.uid);
          if (userData) {
            const userObj: User = {
              id: firebaseUser.uid,
              email: firebaseUser.email!,
              role: userData.role,
              phone: userData.phoneNumber,
              isActive: userData.isActive,
              isVerified: userData.isVerified,
              createdAt: userData.createdAt?.toDate(),
              updatedAt: userData.updatedAt?.toDate(),
            };

            setUser(userObj);
            setStoredAuthState(userObj);

            // Update last login time
            await updateUserLastLogin(firebaseUser.uid);
          } else {
            // User exists in Firebase Auth but not in Firestore
            setUser(null);
            setStoredAuthState(null);
          }
        } else {
          // User is signed out
          setUser(null);
          setStoredAuthState(null);
        }
      } catch (error) {
        console.error('Error in auth state change:', error);
        setUser(null);
        setStoredAuthState(null);
      } finally {
        setLoading(false);
        setInitialAuthCheck(true);
      }
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, name: string, role: User['role']) => {
    setLoading(true);
    try {
      const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);

      // Update Firebase Auth profile
      await updateFirebaseProfile(firebaseUser, { displayName: name });

      // Create user document in Firestore using new database functions
      await createUser(firebaseUser.uid, {
        email,
        role,
        isActive: true,
        isVerified: false,
      });
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      // Clear stored auth state immediately for better UX
      setStoredAuthState(null);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    if (!user) throw new Error('No user logged in');

    try {
      // Update Firestore document using new database functions
      await updateUser(user.id, {
        phoneNumber: data.phone,
        profileImageUrl: data.profileImage,
      });

      // Update local state and storage
      const updatedUser = { ...user, ...data, updatedAt: new Date() };
      setUser(updatedUser);
      setStoredAuthState(updatedUser);
    } catch (error) {
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };

  // Show professional loading screen during initial auth check
  if (!initialAuthCheck) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-2xl">T</span>
            </div>
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading Tap2Go...</p>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
